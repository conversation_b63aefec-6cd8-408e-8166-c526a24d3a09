import {NextIntlClientProvider} from 'next-intl';
import {getMessages} from '@/i18n/routing';
import {notFound, redirect} from 'next/navigation';
import {routing} from '@/i18n/routing';
import '../globals.css';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { getLandingPage } from '@/app/actions';
import { Providers } from '@/app/providers';
import { Metadata } from 'next';
import Script from 'next/script';
// import { SplashCursor } from "@/components/ui/splash-cursor"
import { Toaster } from "@/components/ui/toaster"
import { metadataManager } from '@/lib/seo/metadata-manager';
import { generateWebSiteSchema, generateOrganizationSchema } from '@/lib/seo/schema';


// 从 routing 中获取具体的 locale 类型
type Locale = (typeof routing.locales)[number];

// 修改类型定义，使用 generateStaticParams 来处理参数
export async function generateStaticParams() {
  return routing.locales.map((locale) => ({locale}));
}

// 定义动态元数据生成函数
export async function generateMetadata({ params }: { params: Promise<{ locale: string }> }): Promise<Metadata> {
  const { locale } = await params;

  // Use the enhanced metadata manager
  return metadataManager.getHomeMetadata(locale);
}

// 更新 Layout 组件以支持异步 params
export default async function Layout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  // 使用 await 获取 locale
  const { locale } = await params;
  
  // 验证 locale
  if (!routing.locales.includes(locale as any)) {
    notFound();
  }
 
  // 并行获取消息和页面数据
  const [messages, page] = await Promise.all([
    getMessages(locale),
    getLandingPage(locale)
  ]);

  // Generate schema markup for the page
  const websiteSchema = generateWebSiteSchema(locale);
  const organizationSchema = generateOrganizationSchema(locale);

  return (
    <html lang={locale} className="scroll-smooth">
      {/* 鼠标烟雾 */}
      {/* <SplashCursor /> */}
      <head>
        {/* Schema Markup */}
        <Script
          id="website-schema"
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(websiteSchema),
          }}
        />
        <Script
          id="organization-schema"
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(organizationSchema),
          }}
        />

        {/* Google Analytics */}
        <Script
          src="https://www.googletagmanager.com/gtag/js?id=G-C3V8FGTG3J"
          strategy="afterInteractive"
        />
        <Script id="google-analytics" strategy="afterInteractive">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-C3V8FGTG3J');
          `}
        </Script>
      </head>

      <body suppressHydrationWarning className="min-h-screen">
          <Script
            src="https://umami.wenhaofree.com/script.js"
            data-website-id="b065fdae-866a-45bb-816c-36ef1367f473"
            defer
          />
        
          <NextIntlClientProvider messages={messages} locale={locale}>
          <Providers>
            <div className="fixed inset-x-0 top-0 z-50 h-16 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
                <div className="h-full">
                  {page.header && <Header header={page.header} />}
                </div>
            </div>
            <main className="flex-1 pt-16">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                  {children}
                </div>
              </main>
            <div className="border-t">
              {page.footer && <Footer footer={page.footer} />}
            </div>
            <Toaster />
            </Providers>
          </NextIntlClientProvider>
        
      </body>
    </html>
  );
}